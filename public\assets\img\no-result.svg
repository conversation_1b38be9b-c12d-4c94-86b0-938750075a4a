<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
</style>
</defs>

  <defs>
    <!-- Background gradient -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fafbfc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>

    <!-- Main icon gradient -->
    <linearGradient id="mainIconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" />
    </linearGradient>

    <!-- Secondary elements gradient -->
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#94a3b8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64748b;stop-opacity:1" />
    </linearGradient>

    <!-- Shadow filter -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#000000" flood-opacity="0.1"/>
    </filter>

    <!-- Glow effect -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#backgroundGradient)" rx="16"/>

  <!-- Main search icon circle -->
  <circle cx="200" cy="120" r="45" fill="none" stroke="url(#mainIconGradient)" stroke-width="4" filter="url(#dropShadow)"/>

  <!-- Search handle -->
  <path d="M235 155 L260 180" stroke="url(#mainIconGradient)" stroke-width="4" stroke-linecap="round" filter="url(#dropShadow)"/>

  <!-- Decorative elements -->
  <circle cx="120" cy="80" r="3" fill="url(#secondaryGradient)" opacity="0.6"/>
  <circle cx="280" cy="90" r="2" fill="url(#secondaryGradient)" opacity="0.4"/>
  <circle cx="100" cy="180" r="2.5" fill="url(#secondaryGradient)" opacity="0.5"/>
  <circle cx="300" cy="170" r="2" fill="url(#secondaryGradient)" opacity="0.3"/>

  <!-- Floating dots animation effect -->
  <circle cx="150" cy="60" r="1.5" fill="url(#mainIconGradient)" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="250" cy="70" r="1" fill="url(#mainIconGradient)" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
  </circle>

  <!-- Main text -->
  <text x="200" y="230" text-anchor="middle" class="text-modern" font-size="22" font-weight="700" fill="#1e293b" filter="url(#dropShadow)">No Results Found</text>

  <!-- Subtitle -->
  <text x="200" y="255" text-anchor="middle" class="text-modern" font-size="16" font-weight="400" fill="#64748b">Try adjusting your search criteria or filters</text>

  <!-- Additional helpful text -->
  <text x="200" y="275" text-anchor="middle" class="text-modern" font-size="14" font-weight="400" fill="#94a3b8">We couldn't find what you're looking for</text>
</svg>