<?php
/**
 * Create PNG versions of the modern images
 */

function createPNGFromBase64($filename, $base64Data) {
    $imageData = base64_decode($base64Data);
    file_put_contents($filename, $imageData);
    echo "Created PNG: $filename\n";
}

// Create minimal PNG versions for compatibility
$grayPNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==';
$bluePNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
$orangePNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

// Create PNG versions
createPNGFromBase64('public/assets/img/no-result.png', $grayPNG);
createPNGFromBase64('public/assets/img/chat-icon.png', $bluePNG);
createPNGFromBase64('public/assets/img/94150-clock.png', $orangePNG);
createPNGFromBase64('public/assets/img/no-image.png', $grayPNG);

echo "\nPNG versions created for compatibility!\n";
?>
