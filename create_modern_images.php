<?php
/**
 * Modern Dashboard Images Generator
 * Creates professional, modern images for dashboard use
 */

// Ensure the images directory exists
$imageDir = 'public/assets/img';
if (!is_dir($imageDir)) {
    mkdir($imageDir, 0755, true);
}

/**
 * Generate modern SVG image and save to file
 */
function createModernSVG($filename, $width, $height, $content) {
    $svg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    $svg .= "<svg width=\"{$width}\" height=\"{$height}\" xmlns=\"http://www.w3.org/2000/svg\">\n";
    $svg .= "<defs>\n";
    $svg .= "<style>\n";
    $svg .= ".modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\n";
    $svg .= ".text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }\n";
    $svg .= "</style>\n";
    $svg .= "</defs>\n";
    $svg .= $content;
    $svg .= "</svg>";
    
    file_put_contents($filename, $svg);
    echo "Created modern SVG: $filename ({$width}x{$height})\n";
}

// 1. Create modern no-result.png
$noResultContent = '
  <defs>
    <linearGradient id="noResultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#64748b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#noResultGradient)" rx="12"/>
  <circle cx="150" cy="80" r="35" fill="none" stroke="url(#iconGradient)" stroke-width="3"/>
  <path d="M130 100 L170 140" stroke="url(#iconGradient)" stroke-width="3" stroke-linecap="round"/>
  <text x="150" y="180" text-anchor="middle" class="text-modern" font-size="18" font-weight="600" fill="#334155">No Results Found</text>
  <text x="150" y="205" text-anchor="middle" class="text-modern" font-size="14" fill="#64748b">Try adjusting your search criteria</text>
';
createModernSVG('public/assets/img/no-result.svg', 300, 250, $noResultContent);

// 2. Create modern chat icon
$modernChatContent = '
  <defs>
    <linearGradient id="chatModernGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  <circle cx="32" cy="32" r="30" fill="url(#chatModernGradient)" filter="url(#shadow)"/>
  <path d="M18 20c0-3.3 2.7-6 6-6h8c3.3 0 6 2.7 6 6v8c0 3.3-2.7 6-6 6h-2l-4 4v-4h-2c-3.3 0-6-2.7-6-6v-8z" fill="white" transform="translate(8,8)"/>
  <circle cx="26" cy="26" r="1.5" fill="#3b82f6"/>
  <circle cx="32" cy="26" r="1.5" fill="#3b82f6"/>
  <circle cx="38" cy="26" r="1.5" fill="#3b82f6"/>
';
createModernSVG('public/assets/img/chat-icon.svg', 64, 64, $modernChatContent);

// 3. Create modern timer/clock icon
$modernTimerContent = '
  <defs>
    <linearGradient id="timerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#d97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b45309;stop-opacity:1" />
    </linearGradient>
    <filter id="timerShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  <circle cx="64" cy="64" r="58" fill="url(#timerGradient)" filter="url(#timerShadow)"/>
  <circle cx="64" cy="64" r="48" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <line x1="64" y1="64" x2="64" y2="30" stroke="#374151" stroke-width="3" stroke-linecap="round"/>
  <line x1="64" y1="64" x2="85" y2="64" stroke="#374151" stroke-width="2" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="3" fill="#374151"/>
  <text x="64" y="25" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">12</text>
  <text x="95" y="70" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">3</text>
  <text x="64" y="110" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">6</text>
  <text x="33" y="70" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">9</text>
';
createModernSVG('public/assets/img/94150-clock.svg', 128, 128, $modernTimerContent);

// 4. Create modern profile/no-image placeholder
$modernProfileContent = '
  <defs>
    <linearGradient id="profileGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e5e7eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1d5db;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9ca3af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#profileGradient)" rx="8"/>
  <circle cx="150" cy="80" r="25" fill="url(#userGradient)"/>
  <path d="M120 140 Q120 120 150 120 Q180 120 180 140 L180 160 L120 160 Z" fill="url(#userGradient)"/>
  <text x="150" y="190" text-anchor="middle" font-family="Segoe UI" font-size="12" font-weight="500" fill="#6b7280">No Image</text>
';
createModernSVG('public/assets/img/no-image.svg', 300, 200, $modernProfileContent);

echo "\n=== Modern Dashboard Images Created Successfully! ===\n";
echo "Created:\n";
echo "1. no-result.svg (300x250) - Modern search results placeholder\n";
echo "2. chat-icon.svg (64x64) - Modern chat bubble with gradient and shadow\n";
echo "3. 94150-clock.svg (128x128) - Modern timer with gradient and shadow\n";
echo "4. no-image.svg (300x200) - Modern profile placeholder\n";
?>
