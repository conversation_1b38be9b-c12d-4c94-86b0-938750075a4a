<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
</style>
</defs>

  <defs>
    <linearGradient id="chatModernGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  <circle cx="32" cy="32" r="30" fill="url(#chatModernGradient)" filter="url(#shadow)"/>
  <path d="M18 20c0-3.3 2.7-6 6-6h8c3.3 0 6 2.7 6 6v8c0 3.3-2.7 6-6 6h-2l-4 4v-4h-2c-3.3 0-6-2.7-6-6v-8z" fill="white" transform="translate(8,8)"/>
  <circle cx="26" cy="26" r="1.5" fill="#3b82f6"/>
  <circle cx="32" cy="26" r="1.5" fill="#3b82f6"/>
  <circle cx="38" cy="26" r="1.5" fill="#3b82f6"/>
</svg>