<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.modern-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.text-modern { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
</style>
</defs>

  <defs>
    <linearGradient id="timerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#d97706;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b45309;stop-opacity:1" />
    </linearGradient>
    <filter id="timerShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
  </defs>
  <circle cx="64" cy="64" r="58" fill="url(#timerGradient)" filter="url(#timerShadow)"/>
  <circle cx="64" cy="64" r="48" fill="white" stroke="#f59e0b" stroke-width="2"/>
  <line x1="64" y1="64" x2="64" y2="30" stroke="#374151" stroke-width="3" stroke-linecap="round"/>
  <line x1="64" y1="64" x2="85" y2="64" stroke="#374151" stroke-width="2" stroke-linecap="round"/>
  <circle cx="64" cy="64" r="3" fill="#374151"/>
  <text x="64" y="25" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">12</text>
  <text x="95" y="70" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">3</text>
  <text x="64" y="110" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">6</text>
  <text x="33" y="70" text-anchor="middle" font-family="Segoe UI" font-size="10" font-weight="600" fill="#374151">9</text>
</svg>